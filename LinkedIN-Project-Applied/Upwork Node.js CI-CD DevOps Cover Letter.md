Hi! Your project about automating Node.js deployments on AWS EC2 stood out to me—I've set up Dockerized CI/CD pipelines with GitHub Actions for several clients, so I know how much smoother and safer it can make releases.

Recently, I helped a SaaS team move from manual to automated deployments using Docker, Nginx for SSL, and CloudWatch for monitoring. We achieved zero-downtime rollouts and cut their deployment time in half, all while keeping costs in check and following security best practices.

Do you have a preferred approach for blue/green or rolling deployments, or are you open to suggestions? Let me know if you'd like to chat about the best way to streamline your workflow! 