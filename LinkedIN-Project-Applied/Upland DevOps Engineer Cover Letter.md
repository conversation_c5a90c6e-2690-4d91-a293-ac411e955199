**Subject:** <PERSON><PERSON><PERSON> Engineer - AWS Production Operations & SaaS Infrastructure Expert

Dear Hiring Manager,

I was excited to see your DevOps Engineer opening at Upland India, particularly the focus on managing 24x7 production SaaS environments and driving operational efficiency. That's exactly the kind of work I've been passionate about for over 15 years.

At RouteClouds, where I currently serve as Founder & Solution Architect, I've managed enterprise-level production environments for multiple SaaS clients, including a financial services company where I implemented CI/CD pipelines that reduced deployment time from 2 days to 2 hours while achieving 99.9% success rate across 50+ microservices. I've also architected scalable AWS infrastructure for e-commerce platforms using EKS and ECS, resulting in 30% performance improvements and 25% cost reductions.

My experience includes administering complex Linux-based web hosting configurations, implementing infrastructure-as-code using Terraform and Ansible, and setting up comprehensive monitoring solutions with Prometheus, Grafana, and CloudWatch. I've successfully migrated large-scale applications from on-premises to AWS cloud environments, ensuring high availability and implementing disaster recovery strategies for critical SaaS applications.

I'm curious about the specific challenges your team faces in modernizing legacy applications and improving deployment processes. Are you primarily focused on containerizing existing applications or building new cloud-native solutions?

I'm available to start immediately and would love to discuss how my background in AWS production operations, automation, and SaaS infrastructure management might help drive greater efficiency across your environments.

Best regards,
<PERSON><PERSON> Khan
<EMAIL>
+918756700835 