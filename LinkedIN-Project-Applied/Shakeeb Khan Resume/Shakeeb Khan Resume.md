﻿**<PERSON><PERSON> | Cloud & DevOps Consultant | Corporate Trainer**

**Lucknow UP India-226001 | <EMAIL> | +**************

**Executive Summary**

Proactive Cloud & DevOps Consultant and Corporate Trainer with over 15 years of progressive experience in cloud architecture, CI/CD practices and infrastructure as code. Demonstrates exceptional skills in managing enterprise-level firewalls, implementing DevOps methodologies and leading technical teams. Experienced corporate trainer with specialization in AWS, Azure and GCP certifications.

**Core Competencies & Technical Skills**

- Cloud Architecture & Migration : AWS (EC2, S3, RDS, Lambda, IAM, VPC, ELB/ALB, Route 53, CloudTrail, CloudWatch, KMS, Secrets Manager, Systems Manager, Code Pipeline, Code Build, Code Deploy, Code Commit, Transit Gateway, VPN, Azure, GCP
- DevOps Implementation & CI/CD Pipelines tools : Git, Maven, Jenkins, Ansible, Docker, Kubernetes, Helm and Terraform. 
- Network Security & Firewall Management : Checkpoint Firewalls, Fortinet, VPN, HA & Load Balancers, IPS/IDS, Antivirus, Application Control, Symantec Endpoint Protection
- Monitoring: CloudWatch, Prometheus, Grafana and Nagios monitoring solutions
- Scripting: Bash, Python
- Multicloud and Hybrid Cloud Strategies
- Corporate Training & Technical Education

**Employment Details**

**RouteClouds Founder & Solution Architect                                                           8/2023 – Present**

Lead a cutting-edge cloud consulting firm specializing in seamless migrations, cloud-native solutions and advanced DevOps practices.

- Spearhead the delivery of comprehensive cloud and DevOps solutions, focusing on AWS architecture, CI/CD implementation and infrastructure automation.
- Design and implement tailored cloud migration strategies, resulting in optimized performance and cost-efficiency for clients.
- Conduct advanced training programs in AWS services and DevOps practices, enhancing client teams technical capabilities.
- Develop and implement robust CI/CD pipelines, significantly reducing deployment times and improving software delivery processes.
- Architected and implemented a scalable AWS infrastructure for an e-commerce client, resulting in a 30% increase in website performance and a 25% reduction in operational costs.

**Freelancing Industry, Lucknow UP                                                                             4/2019 – 7/2023**

**Designation: DevOps Consultant | Corporate Trainer**

Provide comprehensive training programs for AWS, Azure and GCP certifications to students, working professionals and corporate clients and provide consulting for Cloud based solutions.

- Developed and delivered AWS Solutions Architect Associate certification training, achieving a 95% pass rate among 200+ students.
- Led a large-scale migration from on-premises to AWS cloud for a retail client, resulting in 40% cost savings and 50% improvement in system performance.
- Conduct hands-on workshops covering key cloud concepts, services and best practices.
- Guide students through practical projects to reinforce learning, including web hosting, database management and serverless applications.
- Offered specialized modules on Linux fundamentals, Network concepts, Python scripting and Bash scripting to enhance students cloud skills.

**FluentGrid, Lucknow UP 						                  1/2019 – 3/2019**

**Designation: Network Solution Architect** 

Worked with FluentGrid for the Lucknow Smart City Project (LSCL) with following roles and responsibilities.

- Designed and implemented the Lucknow Smart City Data Center, including firewalls, IPS/IDS, link load balancers and DR site creation on Government Cloud Meghraj.
- Coordinated with internal and external teams to complete tasks with clients.
- Established reliable connectivity with the data center (DC) for implementing e-Governance and LSCL Citizen Portal.

**Freelancing Industry, Lucknow UP                                                                           1/2018 –12/2018** 

**Designation: IT Consultant**

- Designed, set up and maintained a cloud-based system using AWS services such as EC2, S3, VPC, ELB, Route 53, Terraform, CloudWatch and IAM roles.
- Migrated on-premises infrastructure to the cloud using AWS services, minimizing operational disruption and maximizing ROI.
- Organized educational sessions for clients and team members regarding AWS services and industry best practices.

**HCL Technologies, Lucknow UP                                                                            11/2015 – 11/2017**

**Designation: Technical Specialist** 

Responsible for managing IT security infrastructure for clients, including the following roles and responsibilities.

- Managed security operations, administration and troubleshooting of Checkpoint Firewalls on 12600 series in four data centers of the client's IT infrastructure.
- Oversaw and owned security incidents of Severity 2&3, conducted RCA for these incidents resolution and risk mitigation.
- Collaborated with the GSOC team to manage security incidents, collect events, monitor logs, ensure compliance automation and oversee identity monitoring activities.
- Managed Symantec SEPM protection solution for over 2000 servers by monitoring compliance status and creating security policies and exclusions based on requirements.


**Wipro InfoTech, Lucknow UP                                                                                     1/2013 – 9/2015**

**Designation: Senior Engineer Security Management**

- Managed a team of over 250 network engineers deployed in remote locations as a team lead, providing technical and operational support.
- Responsible for managing IT security operations for 885 remote locations, which included managing security solutions for Checkpoint firewalls on 12600 series and other related technologies.
- Collaborated with government clients and IT users to facilitate optimal utilization of IT Infrastructure services and solutions for effective government service and information management.

**Earlier Career Highlights**

- Security Administrator at TULIP (09/2012 - 12/2012).
- Network Engineer at ICCES, Saudi Arabia (01/2011 - 11/2011).
- Network Engineer at HCL Infosystem (08/2008 - 01/2011).

**Highest Academic Qualification**:

Masters of Computer Application from Integral University, Lucknow in year 2008.      

**Certifications** 

- Symantec Certified Specialist for Symantec Endpoint Protection 12.1.
- Trend Micro Certified Professional for Deep Security for 9.6 version.
- Microsoft® Certified Technology Specialist MCTS Certification: 70-640 Windows Server® 2008 Active Directory Configuration.
- Microsoft Certified Systems Administrator: ID: 5960747.
- Symantec Certified specialist for Backup Exec 2010 R3.
- VMware Technical Sales Professional (VTSP4).
- VMware Sales Professional (VSP5).
- IBM Blade Center Training - XTR14 (2011).
- Cisco Certified Network Associate ID: CSCO11291681.

**Projects Details:**

` `1.Advanced CI/CD Pipeline Implementation

- Objective: Modernize software delivery for a financial services company
- Technologies: Jenkins, Git, Kubernetes, Terraform, Ansible and AWS EKS
- Achievements:
  - Reduced deployment time from 2 days to 2 hours.
  - Improved infrastructure consistency by 100% using Terraform.
  - Reduced resource utilization by 30% with Kubernetes.
  - Achieved 99.9% success rate for automated deployments across 50+ microservices
- Impact: Accelerated time-to-market by 60%, increased customer satisfaction by 25%

2\.Enterprise Migration from On-Premises to AWS Cloud

- Objective: Transition the company’s IT infrastructure from on-premises to a scalable and cost-effective AWS cloud environment.
- Tools Used: AWS Migration Tools and other AWS services, Database Transfer Services, Python, Bash.
- Achievements:
  - Managed the large-scale migration project, ensuring minimal disruption to operations.
  - Established secure connections between legacy systems and the new AWS environment.
  - Automated cloud setup using Python and Bash scripts to streamline processes and reduce manual intervention.
- Impact: Successfully reduced IT costs by 40% and improved system performance by 50%, enhancing overall efficiency and scalability.

3\.Web Application Infrastructure for Gulf Client

- Objective: Create a robust and scalable infrastructure for an e-commerce platform.
- Technologies: AWS EC2, RDS, S3, CloudFront, CodePipeline, CodeBuild, CodeDeploy, WAF, Shield.
- Achievements:
  - Architected and deployed scalable AWS infrastructure tailored for e-commerce operations.
  - Implemented a CI/CD pipeline to automate the deployment process, ensuring continuous delivery and integration.
  - Integrated security measures including AWS WAF and Shield to protect against web threats.
  - Delivered a comprehensive CMS, payment gateway integration and interactive features to enhance user experience.
- Impact: Improved website performance, security and user engagement, supporting business growth and customer satisfaction.

4\.High-Availability Checkpoint Firewall Deployment

- Objective: Enhance network security for a major banking institution
- Technologies: Checkpoint Firewall (6200/6400 series), Gaia R81.10 platform
- Achievements:
- Ensured 99.999% uptime for critical financial transactions.
- Reduced security incidents by 80% with advanced threat prevention.
- Implemented dual ISP link failover for seamless connectivity.
- Impact: Zero security breaches over 24 months, successful regulatory audits.

` `5.Fortinet Infrastructure Upgrade for Government Agency

- Objective: Modernize network security for distributed government offices
- Technologies: FortiManager, FortiAnalyzer, FortiGate, FortiClient
- Achievements:
  - Migrated 85 remote FortiGate Firewalls with zero security gaps
  - Upgrade firmaware on remote firewalls, Improved log processing performance.
  - Deployed FortiClient on 10,000+ endpoints for secure remote access.
- Impact: Reduced IT security management effort by 60%, improved incident response time by 75%.
