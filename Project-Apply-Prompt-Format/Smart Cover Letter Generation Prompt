# Human-Style Cover Letter Generation Prompts

## Prompt 1: For Upwork & Freelance Platforms

```
You're helping me write a casual, conversational cover letter for a freelance project. I want it to sound like I'm genuinely interested and actually read their posting - not like an AI wrote it.

**THE PROJECT POSTING:**
[PASTE THE COMPLETE PROJECT DESCRIPTION HERE]

**MY RELEVANT BACKGROUND:**
[PASTE 3-4 BULLET POINTS OF YOUR MOST RELEVANT EXPERIENCE]

**WRITING GUIDELINES:**
- Write like I'm talking to a potential client, not giving a formal presentation
- Use contractions (I've, you're, can't, etc.) and natural language
- Start by mentioning something specific from their posting that caught my attention
- Share 1-2 brief stories about similar work I've done (with concrete results when possible)
- Ask a thoughtful question about their project or timeline
- Keep it between 80-120 words total
- Avoid these AI-sounding phrases: "I am writing to express interest," "I am confident my skills align," "I would welcome the opportunity"
- Sound enthusiastic but not desperate
- End with something casual like "Let me know if you'd like to chat about this" instead of formal closings

**TONE:** Friendly, competent, like I'm genuinely excited about their project and want to help solve their problem.

Write the cover letter now, making it sound like a real person wrote it.
```

## Prompt 2: For LinkedIn & Corporate Job Applications

```
Help me write a professional but conversational cover letter for a job application. I want it to sound authentic and human-written, not like generic AI output.

**JOB POSTING:**
[PASTE THE COMPLETE JOB DESCRIPTION HERE]

**MY RESUME:** Attached
[PASTE YOUR RELEVANT EXPERIENCE AND SKILLS]

**WRITING INSTRUCTIONS:**
- Write in a professional yet conversational tone - formal enough for corporate but still sounds like a real person
- Open by mentioning something specific about the company or role that genuinely interests me
- Share 2-3 relevant experiences as brief stories with specific outcomes (numbers/results when possible)
- Show I understand what they're looking for and how I can help
- Ask one thoughtful question that demonstrates knowledge of their industry or challenges
- Keep it 120-150 words maximum
- Use some contractions and varied sentence lengths to sound natural
- Avoid robotic phrases like "Please find my resume attached," "Thank you for your consideration," "I am writing to apply"
- Include a clear subject line for email applications

**TONE:** Professional but personable - like I'm someone they'd want to work with daily. Confident without being arrogant.

**STRUCTURE:**
1. Natural opening that shows genuine interest
2. Relevant experience told as brief stories
3. How I can specifically help them
4. Thoughtful question about the role/company
5. Friendly professional closing

Generate the cover letter and subject line now.
```

## Quick Usage Tips:

### For Both Prompts:

**Step 1:** Copy the appropriate prompt above
**Step 2:** Replace the bracketed sections:
- Paste the actual job/project description
- Add your relevant experience (3-4 key points)
**Step 3:** Submit to your preferred AI (ChatGPT, Claude, Gemini)
**Step 4:** Review and personalize further if needed

### What to Include in "MY BACKGROUND" Section:

**For Freelance Projects:**
```
• Recently helped a startup reduce AWS costs by 40% in 3 weeks
• Built 15+ e-commerce sites using React and Node.js
• Specialized in database optimization - improved query speed by 60% for last client
• Available to start immediately, typically deliver projects 2-3 days early
```

**For Corporate Jobs:**
```
• 4 years as Senior Developer at TechCorp, led team of 5 developers
• Launched mobile app that gained 50K users in first month
• Expert in Python, React, and cloud architecture (AWS certified)
• Successfully managed $2M budget for digital transformation project
```

## Sample Expected Outputs:

### Freelance Platform Style:
> "Hi! Your WordPress speed optimization project caught my eye because I just wrapped up something similar for an e-commerce client. Managed to cut their load times from 8 seconds down to under 2 seconds, which boosted their conversion rate by 25%. 
>
> I've been doing WordPress performance work for about 3 years now and love the detective work involved in finding bottlenecks. Most issues I see are related to oversized images and plugin conflicts, but every site's different.
>
> What's your current page speed score, and are you seeing any specific performance issues on mobile vs desktop?
>
> Happy to chat more about this if it sounds like a good fit!"

### Corporate/LinkedIn Style:
> **Subject:** Marketing Data Analyst Position - Python & Analytics Background
>
> "I was excited to see your opening for a Marketing Data Analyst, especially the focus on customer segmentation and predictive analytics. That's exactly the kind of work that got me into data science.
>
> At my current role with RetailCorp, I built a customer segmentation model that helped increase email campaign ROI by 35%. I also developed automated reporting dashboards that saved our marketing team about 10 hours per week. Most of my work involves Python, SQL, and Tableau - which aligns perfectly with your tech stack.
>
> I'm curious about the customer data challenges you're facing as you scale. Are you primarily looking to improve acquisition metrics or focus more on retention analysis?
>
> Would love to discuss how my background might fit with your team's goals.
>
> Best regards,
> [Your Name]"

## Pro Tips:

1. **Always customize** the generated output - add personal touches
2. **Check for repetitive phrases** that might sound AI-generated
3. **Read it aloud** - if it sounds weird spoken, revise it
4. **Vary your examples** - don't use the same projects for every application
5. **Update your background section** regularly with new achievements