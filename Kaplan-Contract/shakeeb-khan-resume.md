<PERSON><PERSON> <PERSON> | Cloud & DevOps Consultant | Corporate Trainer

Lucknow UP India-226001 | <EMAIL> | +************

Professional Summary

Proactive DevOps Consultant with over 15 years of progressive experience in Cloud and Data Centre environments. Adept at designing, implementing, and maintaining secure cloud-based solutions with expertise in AWS services, CI/CD practices and infrastructure as code. Proven track record in managing enterprise-level firewalls and implementing cutting-edge DevOps methodologies. Skilled in team leadership and possessing strong interpersonal communication skills. Experienced corporate trainer specializing in AWS, Azure and GCP certifications.

Core Competencies
•	Cloud Architecture & Migration (AWS, Azure, GCP)
•	DevOps Implementation & CI/CD Pipelines
•	Infrastructure as Code (Terraform, CloudFormation)
•	Container Orchestration (Docker, Kubernetes)
•	Network Security & Firewall Management
•	High Availability & Disaster Recovery
•	Corporate Training & Technical Education
Technical Skills
•	Cloud Platforms: AWS (EC2, S3, RDS, Lambda, IAM, VPC, ELB/ALB, Route 53, CloudTrail, CloudWatch, KMS, Secrets Manager, Systems Manager, Code Pipeline, Code Build, Code Deploy, Code Commit, Direct Connect, Transit Gateway, VPN), Azure, GCP
•	DevOps Tools: Git, Ma<PERSON>, <PERSON>, Ansible, Docker, Kubernetes, Terraform
•	Networking: Checkpoint Firewalls, Fortinet, VPN, Load Balancers
•	Security: IPS/IDS, Antivirus, Application Control, Symantec Endpoint Protection
•	Monitoring: CloudWatch, Prometheus, Grafana, Custom monitoring solutions
•	Scripting: Bash, Python , Operating Systems: Linux, Windows Server
Employment Details
RouteClouds Founder & Solution Architect                                                           8/2023 – Present
RouteClouds specializes in Cloud and DevOps consulting, offering seamless migrations, cloud-native solutions and cutting-edge DevOps practices.
•	Spearhead the delivery of comprehensive cloud and DevOps solutions, focusing on AWS architecture, CI/CD implementation, and infrastructure automation.
•	Design and implement tailored cloud migration strategies, resulting in optimized performance and cost-efficiency for clients.
•	Conduct advanced training programs in AWS services and DevOps practices, enhancing client teams' technical capabilities.
•	Develop and implement robust CI/CD pipelines, significantly reducing deployment times and improving software delivery processes.
•	Architect scalable and secure cloud infrastructures using AWS services, ensuring high availability and disaster recovery capabilities.

Freelancing Industry, Lucknow UP                                                                               4/2019 – 2023

Designation: DevOps Consultant | Corporate Trainer

Provide comprehensive training programs for AWS, Azure and GCP certifications to students, working professionals and corporate clients.

Key Responsibilities:
•	Develop and deliver training curricula for AWS Solutions Architect Associate), certifications, Azure and GCP.
•	Conduct hands-on workshops covering key cloud concepts, services and best practices.
•	Guide students through practical projects to reinforce learning, including web hosting, database management and serverless applications.
•	Offer specialized modules on Linux fundamentals, Python scripting and Bash scripting to enhance students' cloud skills.

Training Highlights:

•	AWS Training: EC2, S3, VPC, IAM, RDS, CloudWatch, Route 53, ELB, Auto Scaling, CloudFront, Lambda, and more.
•	Azure and GCP: Core services, architecture design and migration strategies.
•	DevOps practices: CI/CD pipelines, Infrastructure as Code, and containerization.
•	Real-world projects: Migration and Web hosting, database integration and serverless applications.

FluentGrid, Lucknow UP 						                  1/2019 – 3/2019
Designation: Network Solution Architect 

Worked with FluentGrid for the Lucknow Smart City Project (LSCL) with following roles and responsibilities.

•	As a Network Solution Architect my role was to design and implement Lucknow Smart City (LSCL) Data Center which includes Firewalls, IPS/IDS, Link Load Balancers, Enterprise level endpoint protection, router, switches, DR site creation on Cloud for implementing                   e-Governance and LSCL Citizen Portal.
•	Coordination with internal and external teams responsible for completing tasks with client. 
•	Design and implement a disaster recovery (DR) site on the Government Cloud Meghraj and establish reliable connectivity with the data center (DC).

Freelancing Industry, Lucknow UP                                                                           1/2018 –12/2018 
Designation: IT Consultant

•	Designed, set up and maintained a cloud-based system that could grow and be easily accessed. To achieve this, I used different Amazon Web Services like EC2, S3, VPC, ELB, Route 53, Terraform, CloudWatch and IAM roles.
•	Migrated on-premises infrastructure to the cloud using AWS services, minimizing operational disruption and maximizing return on investment (ROI).
•	Organized educational sessions for clients and team members regarding AWS services and industry best practices, fostering knowledge sharing and promoting ongoing enhancement.



HCL Technologies, Lucknow UP                                                                            11/2015 – 11/2017
Designation: Technical Specialist 

Responsible for managing IT security infrastructure for clients, including the following roles and responsibilities.
•	Managed security operations, administration and troubleshooting of Checkpoint Firewalls on 12600 series in four data centers of the client's IT infrastructure.
•	Oversaw and owned security incidents of Severity 2&3, conducted RCA for these incidents until resolution and risk mitigation.
•	Collaborated with the GSOC team to manage security incidents, collect events, monitor logs, ensure compliance automation and oversee identity monitoring activities.
Managed Symantec SEPM protection solution for over 2000 servers by monitoring compliance status and creating security policies and exclusions based on requirements.

Wipro InfoTech, Lucknow UP                                                                                     1/2013 – 9/2015
Designation: Senior Engineer Security Management

•	Managed a team of over 250 network engineers deployed in remote locations as a team lead, providing technical and operational support.
•	Responsible for managing IT security operations for 885 remote locations, which included managing security solutions for Checkpoint firewalls on 12600 series and other related technologies.
•	Collaborated with government clients and IT users to facilitate optimal utilization of IT Infrastructure services and solutions for effective government service and information management.

Earlier Career Highlights
•	Security Administrator at TULIP (09/2012 - 12/2012)
•	Network Engineer at ICCES, Saudi Arabia (01/2011 - 11/2011)
•	Network Engineer at HCL Infosystem (08/2008 - 01/2011)

Highest Academic Qualification:
Masters of Computer Application from Integral University, Lucknow in year 2008.      

Certifications 

•	Symantec Certified Specialist for Symantec Endpoint Protection 12.1.
•	Trend Micro Certified Professional for Deep Security for 9.6 version.
•	Microsoft® Certified Technology Specialist MCTS Certification: 70-640 Windows Server® 2008 Active Directory Configuration.
•	Microsoft Certified Systems Administrator: ID: 5960747.
•	Symantec Certified specialist for Backup Exec 2010 R3.
•	VMware Technical Sales Professional (VTSP4).
•	VMware Sales Professional (VSP5).
•	IBM Blade Center Training - XTR14 (2011).
•	Cisco Certified Network Associate ID: CSCO11291681.



Projects Details:

1. CI/CD Pipeline Enhancement.

•	Utilized Jenkins, Git, Kubernetes, Terraform, and Ansible to design and implement an advanced CI/CD pipeline.
•	Optimized for performance and cost-efficiency, resulting in faster and more reliable deployments.

•	
2. Enterprise Migration from On-Premises to AWS Cloud.

•	Managed large-scale company transition from local servers to AWS cloud using migration tools and database transfer services.
•	Set up secure connection between old and new systems, used scripts to automate cloud setup (Python, Bash).
•	Reduced IT costs by 40% and improved system speed by 50% after moving to the cloud.
•	
3. Web Application Infrastructure for Gulf client.

•	Designed and deployed scalable AWS infrastructure (EC2, RDS, S3, CloudFront) for e-commerce website.
•	Implemented CI/CD pipeline (CodePipeline, CodeBuild, CodeDeploy) and security measures (WAF, Shield).
•	Delivered CMS, payment integration and interactive features, enhancing user engagement.

4. Checkpoint Firewall Deployment.

•	Deployed Checkpoint firewall appliances (6200 and 6400 series) on Gaia R81.10 platform.
•	Configured HA cluster with dual ISP link failover, integrated management server, and implemented advanced security policies.

5. Fortinet Infrastructure Upgrade.

•	Upgraded FortiManager and FortiAnalyzer appliances for a government client.
•	Successfully migrated 85 remote Fortinet Firewalls to the new management infrastructure.

