Micro1 - <PERSON><PERSON>ps Engineer (LInE)
Required Skills
CI/CD
Docker
Kubernetes
Job Description
Job Title: <PERSON>Ops Engineer

Job Type: Contract (Full-time or Part-time)

Location: Remote



Job Summary:

As a DevOps Engineer, you'll play a pivotal role in optimizing our infrastructure and automating deployments, micro1 offers a unique opportunity to collaborate with industry leaders and embrace cutting-edge technology, all while working from the comfort of your own space. Join us to connect exceptional talent with transformative projects and boost your career growth.



Key Responsibilities:

Develop and maintain robust CI/CD pipelines to streamline deployment processes.
Monitor system health and ensure high availability and reliability of services.
Collaborate with developers to enhance system performance and security.
Implement and manage containerized applications using Docker and Kubernetes.
Identify, troubleshoot, and resolve issues in a timely manner.
Automate infrastructure provisioning and configuration management.
Participate in on-call rotations to support production systems.


Required Skills and Qualifications:

Proficiency in CI/CD tools and practices.
Strong experience with Docker and Kubernetes for container orchestration.
Solid understanding of system monitoring and alerting frameworks.
Excellent written and verbal communication skills.
Demonstrated ability to work independently in a remote setting.
Strong problem-solving and analytical skills.


Preferred Qualifications:

Expertise in cloud platforms such as AWS, Azure, or Google Cloud.
Familiarity with infrastructure as code tools like Terraform or Ansible.
Experience with network security practices and protocols.