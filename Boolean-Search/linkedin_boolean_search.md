# Simple LinkedIn Boolean Search Queries for Cloud and DevOps Roles

## 1. Basic Cloud & DevOps Jobs
```
("<PERSON>Ops Engineer" OR "Cloud Engineer") AND 
("remote" OR "contract")
```

## 2. Basic Training Roles
```
("DevOps Trainer" OR "Cloud Trainer") AND 
("remote" OR "online")
```

## 3. Basic Infrastructure Roles
```
("Infrastructure Engineer" OR "DevOps Engineer") AND 
("remote" OR "contract")
```

## 4. Basic Cloud Roles
```
("Cloud Engineer" OR "Cloud Architect") AND 
("remote" OR "contract")
```

## Search Tips:
1. Start with these basic queries
2. If you get too many results, add one technology at a time:
   - Add: AND (AWS OR Azure)
   - Add: AND (Kubernetes OR Docker)
3. If you get too few results, try:
   - Remove quotes around job titles
   - Use single terms instead of phrases
   - Remove the remote/contract requirement

## Example of Progressive Search:
1. Start with: DevOps Engineer
2. Then try: DevOps Engineer AND remote
3. Then try: <PERSON>Ops Engineer AND remote AND AWS
4. Then try: "DevOps Engineer" AND remote AND AWS

## Alternative Simple Searches:
```
DevOps AND remote
Cloud AND remote
Infrastructure AND remote
```

Remember: Start simple and add complexity only if needed. It's better to get more results and filter manually than to get too few results with complex queries. 