# LinkedIn Boolean Search Queries for DevOps & Cloud Training Roles

## 1. Basic Training Role Searches
```
("DevOps Trainer" OR "Cloud Trainer" OR "Technical Trainer") AND 
("remote" OR "online" OR "virtual")
```

## 2. Platform-Specific Training Roles
```
Trainer AND (AWS OR Azure OR GCP) AND remote
```

## 3. Certification-Focused Training Roles
```
Trainer AND (AWS OR Azure OR Kubernetes) AND remote
```

## 4. Corporate & Enterprise Training Roles
```
Trainer AND (DevOps OR Cloud) AND remote
```

## 5. Content Development Training Roles
```
Developer AND (DevOps OR Cloud) AND remote
```

## 6. Workshop & Bootcamp Roles
```
Facilitator AND (DevOps OR Cloud) AND remote
```

## 7. Multi-Platform Training Roles
```
Trainer AND Cloud AND remote
```

## 8. Specialized Tool Training Roles
```
Trainer AND (Kubernetes OR Docker OR Terraform) AND remote
```

## Search Tips:
1. Start with the basic queries above
2. If you get too many results, add specific technologies:
   - Add: AND (AWS OR Azure OR GCP)
   - Add: AND (Kubernetes OR Docker OR Terraform)
3. If you get too few results:
   - Remove quotes around job titles
   - Try single terms instead of phrases
   - Remove the remote/online requirement

## Alternative Simple Searches:
```
Trainer AND DevOps AND remote
Trainer AND Cloud AND remote
Instructor AND DevOps AND remote
Facilitator AND Cloud AND remote
```

## Progressive Search Strategy:
1. Start with: Trainer AND DevOps
2. Then try: Trainer AND DevOps AND remote
3. Then try: Trainer AND DevOps AND remote AND AWS
4. Then try: Trainer AND DevOps AND remote AND AWS AND Kubernetes

## Additional Keywords to Try:
- "Learning Experience Designer"
- "Technical Evangelist"
- "Certification Mentor"
- "Cloud Adoption Trainer"
- "IT Transformation Trainer"
- "E-Learning Developer"
- "Technical Course Designer"

Remember: 
- Start with simple queries
- Add complexity only if needed
- Use quotes for exact phrases
- Use OR for alternatives
- Use AND to combine requirements 