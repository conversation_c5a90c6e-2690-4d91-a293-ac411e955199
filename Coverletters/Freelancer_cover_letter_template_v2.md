# Cloud & DevOps Project Cover Letter Template

## Template Structure
```
[Role] | [Experience] | [Key Skills] | [Project Focus] | [Availability]
```

## Components Breakdown

### 1. Role Section (15-20 chars)
- Match project title exactly
- Use standard abbreviations
- Examples:
  - "AWS DevOps Expert"
  - "Cloud Architect"
  - "DevOps Engineer"

### 2. Experience Section (8-12 chars)
- Format: "X+ yrs"
- Match project requirements
- Examples:
  - "5+ yrs"
  - "8+ yrs"
  - "10+ yrs"

### 3. Key Skills Section (30-40 chars)
- List 2-3 most relevant skills
- Use project-specific keywords
- Examples:
  - "ECS | Terraform | K8s"
  - "AWS | Azure | GCP"
  - "CI/CD | Docker | Jenkins"

### 4. Project Focus (20-25 chars)
- Highlight main project goal
- Use action verbs
- Examples:
  - "Infra Automation"
  - "Cloud Migration"
  - "DevOps Setup"

### 5. Availability (8-12 chars)
- Match project urgency
- Examples:
  - "Immediate"
  - "Available Now"
  - "Ready to Start"

## Example Cover Letters

### For AWS DevOps Project
```
AWS DevOps Expert | 5+ yrs | ECS | Terraform | Secret Mgr | Immediate
```

### For Cloud Migration Project
```
Cloud Architect | 8+ yrs | AWS | Azure | Migration | Available
```

### For DevOps Setup Project
```
DevOps Engineer | 5+ yrs | CI/CD | Docker | K8s | Ready
```

## Best Practices

1. Project Analysis
   - Read requirements carefully
   - Identify key technologies
   - Note project urgency
   - Understand scope

2. Skill Matching
   - List required skills
   - Prioritize by importance
   - Use exact keywords
   - Match experience level

3. Format Guidelines
   - Use pipe (|) separator
   - Keep it concise
   - Maintain professionalism
   - Verify character count

4. Customization Tips
   - Match project title
   - Use relevant skills
   - Highlight availability
   - Keep it focused

## Character Count Guide
- Total Maximum: 95 characters
- Role: 15-20 chars
- Experience: 8-12 chars
- Skills: 30-40 chars
- Project Focus: 20-25 chars
- Availability: 8-12 chars
- Separators: 4-5 chars
- Spaces: 3-4 chars

## Abbreviations Guide
- Manager → Mgr
- Engineer → Eng
- Professional → Pro
- Available → Avail
- Immediate → Immed
- Infrastructure → Infra
- Development → Dev
- Operations → Ops

## Project-Specific Adaptation Steps
1. Identify project requirements
2. Match skills to needs
3. Determine experience level
4. Check availability requirements
5. Format according to template
6. Verify character count
7. Review for keywords
8. Final check for clarity 